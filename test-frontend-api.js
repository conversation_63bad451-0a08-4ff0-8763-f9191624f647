// Test script to verify frontend can connect to backend API
const axios = require('axios');

async function testFrontendAPI() {
  console.log('Testing frontend API connection...');
  
  try {
    // Test 1: Direct connection to backend
    console.log('\n1. Testing direct backend connection...');
    const directResponse = await axios.post('http://localhost:8000/api/auth/login', {
      email: '<EMAIL>',
      password: 'password'
    });
    console.log('✅ Direct backend connection successful');
    console.log('Response:', directResponse.data);
    
    // Test 2: Connection through frontend proxy
    console.log('\n2. Testing frontend proxy connection...');
    const proxyResponse = await axios.post('http://localhost:3001/api/auth/login', {
      email: '<EMAIL>',
      password: 'password'
    });
    console.log('✅ Frontend proxy connection successful');
    console.log('Response:', proxyResponse.data);
    
  } catch (error) {
    console.error('❌ API test failed:');
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
    } else {
      console.error('Error:', error.message);
    }
  }
}

testFrontendAPI();

<?php

namespace App\Controllers;

use App\Database;
use PDO;

abstract class BaseController
{
    protected PDO $db;
    
    public function __construct()
    {
        $this->db = Database::getConnection();
    }
    
    protected function jsonResponse(array $data, int $statusCode = 200): void
    {
        http_response_code($statusCode);
        echo json_encode($data);
    }
    
    protected function successResponse($data = null, string $message = 'Success'): void
    {
        $response = [
            'success' => true,
            'message' => $message
        ];
        
        if ($data !== null) {
            $response['data'] = $data;
        }
        
        $this->jsonResponse($response);
    }
    
    protected function errorResponse(string $message, int $statusCode = 400, array $errors = []): void
    {
        $response = [
            'success' => false,
            'message' => $message
        ];
        
        if (!empty($errors)) {
            $response['errors'] = $errors;
        }
        
        $this->jsonResponse($response, $statusCode);
    }
    
    protected function getJsonInput(): array
    {
        $input = file_get_contents('php://input');
        return json_decode($input, true) ?? [];
    }
    
    protected function validateRequired(array $data, array $required): array
    {
        $errors = [];
        
        foreach ($required as $field) {
            if (!isset($data[$field]) || empty(trim($data[$field]))) {
                $errors[$field] = ["The {$field} field is required"];
            }
        }
        
        return $errors;
    }
    
    protected function validateEmail(string $email): bool
    {
        return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
    }
    
    protected function getCurrentUser(): ?array
    {
        // Get authorization header from $_SERVER (more reliable with built-in server)
        $authHeader = '';

        // Try different ways to get the Authorization header
        if (isset($_SERVER['HTTP_AUTHORIZATION'])) {
            $authHeader = $_SERVER['HTTP_AUTHORIZATION'];
        } elseif (isset($_SERVER['REDIRECT_HTTP_AUTHORIZATION'])) {
            $authHeader = $_SERVER['REDIRECT_HTTP_AUTHORIZATION'];
        } elseif (function_exists('apache_request_headers')) {
            $headers = apache_request_headers();
            $authHeader = $headers['Authorization'] ?? $headers['authorization'] ?? '';
        } elseif (function_exists('getallheaders')) {
            $headers = getallheaders();
            $authHeader = $headers['Authorization'] ?? $headers['authorization'] ?? '';
        }

        if (!$authHeader || !str_starts_with($authHeader, 'Bearer ')) {
            return null;
        }

        $token = substr($authHeader, 7);

        try {
            // Simple token validation (in production, use JWT)
            // Since SQLite doesn't have MD5, we'll validate in PHP
            $stmt = $this->db->prepare("SELECT u.* FROM users u");
            $stmt->execute();
            $users = $stmt->fetchAll();

            foreach ($users as $user) {
                $userToken = md5($user['id'] . $user['email'] . $user['created_at']);
                if ($userToken === $token) {
                    return $user;
                }
            }

            return null;
        } catch (\Exception $e) {
            return null;
        }
    }
    
    protected function requireAuth(): array
    {
        $user = $this->getCurrentUser();
        
        if (!$user) {
            $this->errorResponse('Unauthorized', 401);
            exit;
        }
        
        return $user;
    }
    
    protected function generateToken(array $user): string
    {
        // Simple token generation (in production, use JWT)
        return md5($user['id'] . $user['email'] . $user['created_at']);
    }
}

<?php

namespace App\Controllers;

class DebugController extends BaseController
{
    public function headers(): void
    {
        $authHeader = '';
        
        // Try different ways to get the Authorization header
        if (isset($_SERVER['HTTP_AUTHORIZATION'])) {
            $authHeader = $_SERVER['HTTP_AUTHORIZATION'];
        } elseif (isset($_SERVER['REDIRECT_HTTP_AUTHORIZATION'])) {
            $authHeader = $_SERVER['REDIRECT_HTTP_AUTHORIZATION'];
        } elseif (function_exists('apache_request_headers')) {
            $headers = apache_request_headers();
            $authHeader = $headers['Authorization'] ?? $headers['authorization'] ?? '';
        } elseif (function_exists('getallheaders')) {
            $headers = getallheaders();
            $authHeader = $headers['Authorization'] ?? $headers['authorization'] ?? '';
        }
        
        $this->successResponse([
            'auth_header' => $authHeader,
            'server_vars' => array_filter($_SERVER, function($key) {
                return strpos($key, 'HTTP_') === 0 || strpos($key, 'AUTH') !== false;
            }, ARRAY_FILTER_USE_KEY),
            'getallheaders' => function_exists('getallheaders') ? getallheaders() : 'not available'
        ]);
    }
    
    public function testToken(): void
    {
        // Get a user from database and generate token
        try {
            $stmt = $this->db->prepare("SELECT * FROM users WHERE email = ?");
            $stmt->execute(['<EMAIL>']);
            $user = $stmt->fetch();

            if ($user) {
                $token = $this->generateToken($user);

                $this->successResponse([
                    'user_id' => $user['id'],
                    'user_email' => $user['email'],
                    'user_created_at' => $user['created_at'],
                    'generated_token' => $token,
                    'token_parts' => [
                        'id' => $user['id'],
                        'email' => $user['email'],
                        'created_at' => $user['created_at'],
                        'concatenated' => $user['id'] . $user['email'] . $user['created_at'],
                        'md5' => md5($user['id'] . $user['email'] . $user['created_at'])
                    ]
                ]);
            } else {
                $this->errorResponse('User not found');
            }
        } catch (\Exception $e) {
            $this->errorResponse('Error: ' . $e->getMessage());
        }
    }
}

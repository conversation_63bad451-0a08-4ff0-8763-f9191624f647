@echo off
echo Testing verify endpoint with curl...

echo.
echo 1. First, login to get a token:
curl -X POST http://localhost:8000/api/auth/login -H "Content-Type: application/json" -d "{\"email\":\"<EMAIL>\",\"password\":\"password\"}"

echo.
echo.
echo 2. Now testing verify endpoint with token:
curl -X GET http://localhost:8000/api/auth/verify -H "Authorization: Bearer 88a1b918d60bc8debd19e343757ea4e7"

echo.
echo Done.

// Test script to check the verify endpoint response structure
const http = require('http');

function testVerifyEndpoint() {
  // First login to get a token
  const loginOptions = {
    hostname: 'localhost',
    port: 8000,
    path: '/api/auth/login',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    }
  };

  const loginData = JSON.stringify({
    email: '<EMAIL>',
    password: 'password'
  });

  const loginReq = http.request(loginOptions, (res) => {
    let data = '';

    res.on('data', (chunk) => {
      data += chunk;
    });

    res.on('end', () => {
      const loginResponse = JSON.parse(data);
      console.log('Login Response:', JSON.stringify(loginResponse, null, 2));
      
      if (loginResponse.success && loginResponse.data && loginResponse.data.token) {
        const token = loginResponse.data.token;
        console.log('\nTesting verify endpoint with token:', token);
        
        // Now test the verify endpoint
        const verifyOptions = {
          hostname: 'localhost',
          port: 8000,
          path: '/api/auth/verify',
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,
          }
        };

        const verifyReq = http.request(verifyOptions, (verifyRes) => {
          let verifyData = '';

          verifyRes.on('data', (chunk) => {
            verifyData += chunk;
          });

          verifyRes.on('end', () => {
            const verifyResponse = JSON.parse(verifyData);
            console.log('Verify Response:', JSON.stringify(verifyResponse, null, 2));
          });
        });

        verifyReq.on('error', (e) => {
          console.error('Verify Error:', e.message);
        });

        verifyReq.end();
      }
    });
  });

  loginReq.on('error', (e) => {
    console.error('Login Error:', e.message);
  });

  loginReq.write(loginData);
  loginReq.end();
}

console.log('Testing verify endpoint...');
testVerifyEndpoint();
